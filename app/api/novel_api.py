from flask import Flask, jsonify, request
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NovelAPI:
    """
    API for accessing novel data.
    """

    def __init__(self, novel_storage, novel_dirs=None, host='0.0.0.0', port=5000):
        """
        Initialize the API with storage.

        Args:
            novel_storage: Instance of NovelStorage
            novel_dirs: List of directories being monitored (optional)
            host: Host to bind the API server
            port: Port to bind the API server
        """
        self.storage = novel_storage
        self.novel_dirs = novel_dirs or []
        self.host = host
        self.port = port
        self.app = Flask(__name__, static_folder="/app/app/static")
        self._setup_routes()

    def _setup_routes(self):
        """Set up API routes."""

        @self.app.route('/api/novels/search', methods=['GET'])
        def search_novels():
            # Get query parameter
            query = request.args.get('q', '')
            logger.info(f"Received search query: {query}")

            # Use the storage.search_novels method directly
            results = self.storage.search_novels(query)

            for novel in results:
                novel['cover_url'] = "/static/book_cover.jpg"

            logger.info(f"Search for '{query}' returned {len(results)} novels")
            return jsonify({'results': results})

        @self.app.route('/api/novels/<int:novel_id>/chapters', methods=['GET'])
        def get_novel_chapters(novel_id):
            novel = self.storage.get_novel_chapters(novel_id)
            if not novel:
                return jsonify({'error': 'Novel not found'}), 404

            return jsonify(novel)

        @self.app.route('/api/chapters/<int:chapter_id>', methods=['GET'])
        def get_chapter_content(chapter_id):
            chapter = self.storage.get_chapter_content(chapter_id)
            if not chapter:
                return jsonify({'error': 'Chapter not found'}), 404

            return jsonify(chapter)

        @self.app.route('/api/status', methods=['GET'])
        def get_status():
            return jsonify({'status': 'running'})

        @self.app.route('/api/folders', methods=['GET'])
        def get_folders():
            """Get the folder structure of monitored directories."""
            try:
                folder_structure = self.storage.get_folder_structure(self.novel_dirs)
                logger.info(f"Retrieved folder structure with {len(folder_structure)} directories")
                return jsonify({'folders': folder_structure})
            except Exception as e:
                logger.error(f"Error getting folder structure: {str(e)}")
                return jsonify({'error': 'Failed to get folder structure'}), 500

        @self.app.route('/api/folders/search', methods=['GET'])
        def search_novels_in_folder():
            """Search for novels in a specific folder."""
            try:
                # Get query parameters
                folder_path = request.args.get('folder', '')
                query = request.args.get('q', '')

                if not folder_path:
                    return jsonify({'error': 'Folder path is required'}), 400

                logger.info(f"Searching for '{query}' in folder: {folder_path}")

                # Search novels in the specified folder
                results = self.storage.search_novels_in_folder(folder_path, query)

                # Add cover URL to each result
                for novel in results:
                    novel['cover_url'] = "/static/book_cover.jpg"

                logger.info(f"Search for '{query}' in folder '{folder_path}' returned {len(results)} novels")
                return jsonify({'results': results, 'folder': folder_path, 'query': query})

            except Exception as e:
                logger.error(f"Error searching novels in folder: {str(e)}")
                return jsonify({'error': 'Failed to search novels in folder'}), 500

    def start(self):
        """Start the API server."""
        logger.info(f"Starting Novel API server on {self.host}:{self.port}")
        self.app.run(host=self.host, port=self.port)

    def get_app(self):
        """Get the Flask app instance."""
        return self.app
